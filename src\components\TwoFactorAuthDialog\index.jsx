import React, { useState, useEffect } from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Typography,
  TextField,
  Button,
  Box,
  Stack,
  IconButton,
  Alert,
  CircularProgress,
  Divider,
} from "@mui/material";
import { styled } from "@mui/material/styles";
import CloseIcon from "@mui/icons-material/Close";
import SecurityIcon from "@mui/icons-material/Security";
import EmailIcon from "@mui/icons-material/Email";
import VerifiedUserIcon from "@mui/icons-material/VerifiedUser";
import { useTranslation } from "react-i18next";
import { toast } from "react-toastify";

// 自定义样式的Dialog
const StyledDialog = styled(Dialog)(({ theme }) => ({
  "& .MuiDialog-paper": {
    borderRadius: "16px",
    padding: theme.spacing(1),
    maxWidth: "480px",
    width: "100%",
    margin: theme.spacing(2),
  },
}));

// 自定义样式的DialogTitle
const StyledDialogTitle = styled(DialogTitle)(({ theme }) => ({
  padding: theme.spacing(3, 3, 1, 3),
  display: "flex",
  alignItems: "center",
  justifyContent: "space-between",
  "& .MuiTypography-root": {
    fontWeight: 600,
    fontSize: "1.25rem",
    color: theme.palette.text.primary,
  },
}));

// 验证码输入框容器
const CodeInputContainer = styled(Box)(({ theme }) => ({
  display: "flex",
  gap: theme.spacing(1),
  justifyContent: "center",
  marginTop: theme.spacing(2),
  marginBottom: theme.spacing(2),
}));

// 单个验证码输入框
const CodeInput = styled(TextField)(({ theme }) => ({
  "& .MuiOutlinedInput-root": {
    width: "48px",
    height: "48px",
    "& input": {
      textAlign: "center",
      fontSize: "1.25rem",
      fontWeight: "bold",
      padding: 0,
    },
    "&.Mui-focused": {
      "& .MuiOutlinedInput-notchedOutline": {
        borderColor: theme.palette.primary.main,
        borderWidth: "2px",
      },
    },
  },
}));

// 信息卡片样式
const InfoCard = styled(Box)(({ theme }) => ({
  backgroundColor: theme.palette.grey[50],
  borderRadius: "12px",
  padding: theme.spacing(2),
  marginBottom: theme.spacing(2),
  border: `1px solid ${theme.palette.grey[200]}`,
}));

const TwoFactorAuthDialog = ({
  open,
  onClose,
  onVerify,
  email,
  password,
  loading = false,
  onResendCode,
}) => {
  const { t } = useTranslation();
  const [code, setCode] = useState(["", "", "", "", "", ""]);
  const [countdown, setCountdown] = useState(0);
  const [error, setError] = useState("");
  const [isResending, setIsResending] = useState(false);

  // 倒计时效果
  useEffect(() => {
    let timer;
    if (countdown > 0) {
      timer = setTimeout(() => setCountdown(countdown - 1), 1000);
    }
    return () => clearTimeout(timer);
  }, [countdown]);

  // 处理验证码输入
  const handleCodeChange = (index, value) => {
    if (value.length > 1) return; // 只允许输入一个字符

    const newCode = [...code];
    newCode[index] = value;
    setCode(newCode);
    setError(""); // 清除错误信息

    // 自动跳转到下一个输入框
    if (value && index < 5) {
      const nextInput = document.getElementById(`code-input-${index + 1}`);
      if (nextInput) nextInput.focus();
    }
  };

  // 处理键盘事件
  const handleKeyDown = (index, event) => {
    if (event.key === "Backspace" && !code[index] && index > 0) {
      const prevInput = document.getElementById(`code-input-${index - 1}`);
      if (prevInput) prevInput.focus();
    }
    if (event.key === "Enter" && code.every((c) => c)) {
      handleVerify();
    }
  };

  // 处理粘贴事件
  const handlePaste = (event) => {
    event.preventDefault();
    const pastedData = event.clipboardData.getData("text").slice(0, 6);
    const newCode = pastedData.split("").concat(Array(6).fill("")).slice(0, 6);
    setCode(newCode);
    setError("");
  };

  // 验证验证码
  const handleVerify = () => {
    const verificationCode = code.join("");
    if (verificationCode.length !== 6) {
      setError("请输入完整的6位验证码");
      return;
    }
    onVerify(verificationCode);
  };

  // 重新发送验证码
  const handleResendCode = async () => {
    if (countdown > 0) return;

    setIsResending(true);
    try {
      await onResendCode();
      setCountdown(60); // 60秒倒计时
      setCode(["", "", "", "", "", ""]);
      setError("");
      toast.success("验证码已重新发送");
    } catch (error) {
      toast.error("发送验证码失败，请稍后重试");
    } finally {
      setIsResending(false);
    }
  };

  // 关闭弹窗时重置状态
  const handleClose = () => {
    setCode(["", "", "", "", "", ""]);
    setError("");
    setCountdown(0);
    onClose();
  };

  return (
    <StyledDialog
      open={open}
      onClose={handleClose}
      maxWidth="sm"
      fullWidth
      disableEscapeKeyDown={loading}>
      <StyledDialogTitle>
        <Box display="flex" alignItems="center" gap={1}>
          <SecurityIcon color="primary" />
          <Typography>双因子身份验证</Typography>
        </Box>
        <IconButton
          onClick={handleClose}
          disabled={loading}
          size="small"
          sx={{ color: "grey.500" }}>
          <CloseIcon />
        </IconButton>
      </StyledDialogTitle>

      <DialogContent sx={{ px: 3, pb: 2 }}>
        {/* 双因子认证说明 */}
        <InfoCard>
          <Box display="flex" alignItems="flex-start" gap={2}>
            <VerifiedUserIcon color="primary" sx={{ mt: 0.5 }} />
            <Box>
              <Typography variant="subtitle2" fontWeight="600" gutterBottom>
                什么是双因子身份验证？
              </Typography>
              <Typography
                variant="body2"
                color="text.secondary"
                sx={{ lineHeight: 1.6 }}>
                双因子身份验证（2FA）是一种额外的安全保护措施，除了密码之外，还需要通过邮箱验证码来确认您的身份，
                大大提高账户的安全性，防止未经授权的访问。
              </Typography>
            </Box>
          </Box>
        </InfoCard>

        <Divider sx={{ my: 2 }} />

        {/* 邮箱信息 */}
        <Box display="flex" alignItems="center" gap={1} mb={2}>
          <EmailIcon color="action" />
          <Typography variant="body2" color="text.secondary">
            验证码已发送至：
          </Typography>
          <Typography variant="body2" fontWeight="600">
            {email}
          </Typography>
        </Box>

        {/* 验证码输入 */}
        <Typography variant="body1" fontWeight="600" textAlign="center" mb={1}>
          请输入6位验证码
        </Typography>

        <CodeInputContainer>
          {code.map((digit, index) => (
            <CodeInput
              key={index}
              id={`code-input-${index}`}
              value={digit}
              onChange={(e) => handleCodeChange(index, e.target.value)}
              onKeyDown={(e) => handleKeyDown(index, e)}
              onPaste={index === 0 ? handlePaste : undefined}
              inputProps={{
                maxLength: 1,
                pattern: "[0-9]*",
                inputMode: "numeric",
              }}
              disabled={loading}
              error={!!error}
            />
          ))}
        </CodeInputContainer>

        {/* 错误信息 */}
        {error && (
          <Alert severity="error" sx={{ mt: 1, mb: 2 }}>
            {error}
          </Alert>
        )}

        {/* 重新发送验证码 */}
        <Box textAlign="center" mt={2}>
          <Typography variant="body2" color="text.secondary" mb={1}>
            没有收到验证码？
          </Typography>
          <Button
            variant="text"
            onClick={handleResendCode}
            disabled={countdown > 0 || isResending || loading}
            startIcon={isResending ? <CircularProgress size={16} /> : null}>
            {countdown > 0
              ? `重新发送 (${countdown}s)`
              : isResending
              ? "发送中..."
              : "重新发送验证码"}
          </Button>
        </Box>
      </DialogContent>

      <DialogActions sx={{ px: 3, pb: 3, pt: 1 }}>
        <Stack direction="row" spacing={2} width="100%">
          <Button
            variant="outlined"
            onClick={handleClose}
            disabled={loading}
            fullWidth
            sx={{ height: 48 }}>
            取消
          </Button>
          <Button
            variant="contained"
            onClick={handleVerify}
            disabled={loading || code.some((c) => !c)}
            fullWidth
            sx={{
              height: 48,
              background: "linear-gradient(90deg, #78BC27, #1487CB)",
              "&:hover": {
                background: "linear-gradient(90deg, #6BA322, #1276B8)",
              },
            }}
            startIcon={
              loading ? <CircularProgress size={20} color="inherit" /> : null
            }>
            {loading ? "验证中..." : "验证"}
          </Button>
        </Stack>
      </DialogActions>
    </StyledDialog>
  );
};

export default TwoFactorAuthDialog;
