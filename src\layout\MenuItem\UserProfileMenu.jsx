import React, { useEffect, useState, useCallback } from "react";
import { useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { useFormik } from "formik";
import { toast } from "react-toastify";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>ton,
  Switch,
  FormControlLabel,
  Card,
  CardContent,
  Divider,
  Box,
  Alert,
  Chip,
} from "@mui/material";
import {
  VpnKey,
  Security,
  Person,
  Email,
  Phone,
  Shield,
} from "@mui/icons-material";

// 组件
import CustomInput from "@c/CustInput.jsx";
import CustomePhoneFiled from "@c/CustomePhoneFiled";
import SubmitButton from "@c/SubmitButton";
import AvatarUploader from "@/components/AvatarUploader";
import PasswordChangeDialog from "@/components/PasswordChange/PasswordChangeDialog";

// 服务和工具
import { RespCode } from "@/enums/RespCode.js";
import {
  changeUserInfo,
  changeUserPassword,
  verifyPassword,
} from "@/service/api/user.js";
import { useStateUserInfo } from "@/hooks/user.js";

function UserProfileMenu() {
  const navigate = useNavigate();
  const { t } = useTranslation();
  const userInfo = useStateUserInfo();

  // 状态管理
  const [loading, setLoading] = useState(false);
  const [imageUrl, setImageUrl] = useState("");
  const [fileUrl, setFileUrl] = useState("");
  const [isRemove, setIsRemove] = useState("0");
  const [dialogOpen, setDialogOpen] = useState(false);
  const [twoFactorEnabled, setTwoFactorEnabled] = useState(false);
  const [twoFactorLoading, setTwoFactorLoading] = useState(false);

  // 处理头像上传
  const handleUpload = (file) => {
    setFileUrl(file);
    const reader = new FileReader();
    reader.onload = (e) => {
      setImageUrl(e.target.result);
    };
    reader.readAsDataURL(file);
  };

  // 表单处理
  const formik = useFormik({
    initialValues: {
      email: "",
      firstName: "",
      lastName: "",
      countryCode: "",
      phone: "",
    },
    onSubmit: async (values, { setErrors, setStatus, setSubmitting }) => {
      try {
        setLoading(true);

        const formData = new FormData();
        formData.append("multipartFile", fileUrl);
        formData.append("firstName", values.firstName);
        formData.append("lastName", values.lastName);
        formData.append("email", values.email);
        formData.append("countryCode", values.countryCode);
        formData.append("phone", values.phone);

        if (isRemove === "1") {
          formData.append("isRemove", isRemove);
        }

        const res = await changeUserInfo(formData);

        if (res?.code === RespCode.SUCCESS) {
          toast.success(t("common.update_success"));
          navigate("/menuItem/userprofile");
        } else {
          toast.error(res?.message || t("common.update_failed"));
        }
      } catch (err) {
        setErrors({ submit: err.message });
        toast.error(t("common.update_failed"));
      } finally {
        setStatus({ success: false });
        setLoading(false);
        setSubmitting(false);
      }
    },
  });

  // 加载用户数据
  useEffect(() => {
    if (userInfo) {
      formik.setFieldValue("firstName", userInfo.firstName || "");
      formik.setFieldValue("lastName", userInfo.lastName || "");
      formik.setFieldValue("email", userInfo.email || "");
      formik.setFieldValue("countryCode", userInfo.countryCode || "");
      formik.setFieldValue("phone", userInfo.phone || "");
      setImageUrl(userInfo.photo || "");

      // 初始化双因子登录状态
      setTwoFactorEnabled(userInfo.twoFactorEnabled || false);
    }
  }, [userInfo]);

  // 处理头像移除
  const handleRemove = () => {
    setIsRemove("1");
  };

  // 处理双因子登录开关
  const handleTwoFactorToggle = useCallback(
    async (enabled) => {
      setTwoFactorLoading(true);
      try {
        // 这里调用API来启用/禁用双因子登录
        // const response = await toggleTwoFactorAuth(enabled);

        // 模拟API调用
        await new Promise((resolve) => setTimeout(resolve, 1000));

        setTwoFactorEnabled(enabled);
        toast.success(
          enabled
            ? t("security.two_factor_enabled") || "双因子登录已启用"
            : t("security.two_factor_disabled") || "双因子登录已禁用"
        );
      } catch (error) {
        toast.error(
          t("security.two_factor_toggle_failed") || "双因子登录设置失败"
        );
      } finally {
        setTwoFactorLoading(false);
      }
    },
    [t]
  );

  return (
    <Box
      sx={{
        width: "100%",
        height: "100%",
        display: "flex",
        flexDirection: "column",
        gap: 2,
      }}>
      {/* 页面标题 */}
      <Card
        sx={{
          position: "sticky",
          top: 0,
          zIndex: 50,
          height: 80,
          display: "flex",
          alignItems: "center",
          borderRadius: 3,
          boxShadow: 1,
        }}>
        <CardContent sx={{ py: 2 }}>
          <Typography variant="h4" fontWeight="600" color="primary">
            {t("common.my_profile")}
          </Typography>
        </CardContent>
      </Card>

      {/* 主内容区 */}
      <Card sx={{ flex: 1, borderRadius: 3, boxShadow: 2, overflowY: "auto" }}>
        <CardContent sx={{ p: 0 }}>
          <form noValidate onSubmit={formik.handleSubmit}>
            {/* 头像上传区 */}
            <Box sx={{ p: 3, borderBottom: 1, borderColor: "divider" }}>
              <Typography variant="h6" fontWeight="600" mb={2}>
                个人头像
              </Typography>
              <AvatarUploader
                imageUrl={imageUrl}
                setImageUrl={setImageUrl}
                onRemove={handleRemove}
                handleUpload={handleUpload}
              />
            </Box>

            {/* 基本信息区 */}
            <Box sx={{ p: 3, borderBottom: 1, borderColor: "divider" }}>
              <Box display="flex" alignItems="center" gap={1} mb={3}>
                <Person color="primary" />
                <Typography variant="h6" fontWeight="600">
                  {t("common.common_base_info")}
                </Typography>
              </Box>

              <Grid container xs={12} spacing={4}>
                <Grid item xs={6}>
                  <CustomInput
                    required
                    name="firstName"
                    formik={formik}
                    label={t("branch_user.firstName")}
                    placeholder={t("branch_user.enter_firstName")}
                  />
                </Grid>
                <Grid item xs={6}>
                  <CustomInput
                    required
                    name="lastName"
                    formik={formik}
                    label={t("branch_user.lastName")}
                    placeholder={t("branch_user.enter_lastName")}
                  />
                </Grid>
                <Grid item xs={6}>
                  <CustomInput
                    required
                    name="email"
                    formik={formik}
                    label={t("common.common_email")}
                    disabled={true}
                    placeholder={t("common.enter_email")}
                  />
                </Grid>
                <Grid item xs={6}>
                  <CustomePhoneFiled
                    label={t("common.common_mobile")}
                    placeholder={t("common.enter_mobile")}
                    name="phone"
                    size="small"
                    className="bg-gray-50"
                    formik={formik}
                    required
                    disabled={true}
                    countryCode={formik.values.countryCode}
                    value={formik.values.phone}
                    handleCountryCode={(e) =>
                      formik.setFieldValue("countryCode", e.dialCode)
                    }
                    handleChange={(e) =>
                      formik.setFieldValue("phone", e.target.value)
                    }
                  />
                </Grid>
              </Grid>
            </Box>

            {/* 安全设置区 */}
            <Box sx={{ p: 3 }}>
              <Box display="flex" alignItems="center" gap={1} mb={3}>
                <Security color="primary" />
                <Typography variant="h6" fontWeight="600">
                  {t("common.common_security")}
                </Typography>
              </Box>

              <Box className="space-y-6">
                {/* 密码修改 */}
                <Card variant="outlined" sx={{ borderRadius: 2 }}>
                  <CardContent>
                    <Box
                      display="flex"
                      alignItems="center"
                      justifyContent="space-between">
                      <Box display="flex" alignItems="center" gap={2}>
                        <VpnKey color="action" />
                        <Box>
                          <Typography variant="subtitle1" fontWeight="500">
                            {t("common.common_change_password")}
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            定期更改密码以保护您的账户安全
                          </Typography>
                        </Box>
                      </Box>
                      <Button
                        variant="contained"
                        onClick={() => setDialogOpen(true)}
                        sx={{
                          background:
                            "linear-gradient(270deg, #1487CA 0%, #78BC27 100%)",
                          "&:hover": {
                            background:
                              "linear-gradient(270deg, #1276B8 0%, #6BA322 100%)",
                          },
                        }}>
                        修改密码
                      </Button>
                    </Box>
                  </CardContent>
                </Card>

                {/* 双因子登录 */}
                <Card variant="outlined" sx={{ borderRadius: 2 }}>
                  <CardContent>
                    <Box
                      display="flex"
                      alignItems="center"
                      justifyContent="space-between">
                      <Box display="flex" alignItems="center" gap={2}>
                        <Shield color="action" />
                        <Box>
                          <Box
                            display="flex"
                            alignItems="center"
                            gap={1}
                            mb={0.5}>
                            <Typography variant="subtitle1" fontWeight="500">
                              双因子身份验证
                            </Typography>
                            {twoFactorEnabled && (
                              <Chip
                                label="已启用"
                                size="small"
                                color="success"
                                variant="outlined"
                              />
                            )}
                          </Box>
                          <Typography variant="body2" color="text.secondary">
                            通过邮箱验证码为您的账户添加额外的安全保护
                          </Typography>
                          {twoFactorEnabled && (
                            <Alert severity="info" sx={{ mt: 1, py: 0.5 }}>
                              登录时需要输入邮箱验证码进行二次验证
                            </Alert>
                          )}
                        </Box>
                      </Box>
                      <FormControlLabel
                        control={
                          <Switch
                            checked={twoFactorEnabled}
                            onChange={(e) =>
                              handleTwoFactorToggle(e.target.checked)
                            }
                            disabled={twoFactorLoading}
                            color="primary"
                          />
                        }
                        label=""
                        sx={{ m: 0 }}
                      />
                    </Box>
                  </CardContent>
                </Card>
              </Box>
            </Box>

            {/* 提交按钮区 */}
            <Box
              sx={{
                px: 3,
                py: 2,
                display: "flex",
                justifyContent: "flex-end",
              }}>
              <SubmitButton
                formik={formik}
                loading={loading}
                callbackRoute={-1}
              />
            </Box>
          </form>
        </CardContent>
      </Card>

      {/* 密码修改对话框 */}
      <PasswordChangeDialog
        open={dialogOpen}
        onClose={() => setDialogOpen(false)}
        title={t("common.common_change_password")}
        currentPasswordLabel={t("common.current_password")}
        newPasswordLabel={t("common.new_password")}
        confirmPasswordLabel={t("common.common_confirm_password")}
      />
    </Box>
  );
}

export default UserProfileMenu;
